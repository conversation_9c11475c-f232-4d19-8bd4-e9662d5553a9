query = (
    "MATCH p = shortestPath((start)-[*]-(end)) "
    "WHERE start.label IN $labels AND end.label IN $labels AND start <> end "
    "RETURN nodes(p) AS nodes, relationships(p) AS edges"
)
result = session.run(query, labels=labels)
paths = []
for record in result:
    paths.append({
        'nodes': [node for node in record['nodes']],
        'edges': [edge for edge in record['edges']]
    })
return paths