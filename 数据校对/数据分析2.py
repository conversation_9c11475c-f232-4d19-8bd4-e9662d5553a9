import pandas as pd
import numpy as np
from sklearn.metrics import (
    accuracy_score,
    precision_recall_fscore_support,
    confusion_matrix,
    classification_report
)
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import matplotlib.pyplot as plt
import os
from pathlib import Path

plt.rcParams['font.sans-serif'] = ['Malgun Gothic', 'SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False


def read_excel_file(file_path):
    """
    读取Excel文件

    Args:
        file_path (str): Excel文件路径

    Returns:
        pandas.DataFrame: 读取的数据框
    """
    try:
        df = pd.read_excel(file_path)
        print(f"成功读取文件: {file_path}")
        print(f"数据维度: {df.shape}")
        return df
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return None


def validate_columns(df, true_col='norm_syndrome', pred_col='answer'):
    """
    验证所需列是否存在

    Args:
        df (pandas.DataFrame): 数据框
        true_col (str): 真实标签列名称（dia列）
        pred_col (str): 预测标签列名称（answer列）

    Returns:
        bool: 是否验证通过
    """
    if true_col not in df.columns:
        print(f"错误: 真实标签列 '{true_col}' 不存在于数据中")
        print(f"可用列: {list(df.columns)}")
        return False

    if pred_col not in df.columns:
        print(f"错误: 预测标签列 '{pred_col}' 不存在于数据中")
        print(f"可用列: {list(df.columns)}")
        return False

    return True


def preprocess_data(df, true_col='norm_syndrome', pred_col='answer'):
    """
    预处理数据，处理缺失值和异常值

    Args:
        df (pandas.DataFrame): 原始数据框
        true_col (str): 真实标签列名（dia列）
        pred_col (str): 预测标签列名（answer列）

    Returns:
        tuple: (y_true, y_pred, 有效数据行数, 匹配统计)
    """
    print("\n=== 数据预处理 ===")

    # 检查原始数据状态
    print(f"原始数据行数: {len(df)}")
    print(f"真实值列 '{true_col}' 缺失值: {df[true_col].isnull().sum()}")
    print(f"预测值列 '{pred_col}' 缺失值: {df[pred_col].isnull().sum()}")

    # 删除任一列为空的行
    clean_df = df.dropna(subset=[true_col, pred_col])
    print(f"删除缺失值后行数: {len(clean_df)}")

    # 转换为字符串类型以确保一致性，并去除前后空格
    y_true = clean_df[true_col].astype(str).str.strip().values
    y_pred = clean_df[pred_col].astype(str).str.strip().values

    # 计算匹配统计
    matches = (y_true == y_pred)
    match_count = matches.sum()
    total_count = len(y_true)
    match_rate = match_count / total_count if total_count > 0 else 0

    print(f"\n=== 匹配情况统计 ===")
    print(f"总样本数: {total_count}")
    print(f"完全匹配数: {match_count}")
    print(f"不匹配数: {total_count - match_count}")
    print(f"匹配率: {match_rate:.4f} ({match_rate * 100:.2f}%)")

    # 显示标签分布
    print(f"\n真实标签 ('{true_col}') 分布:")
    true_counts = Counter(y_true)
    for label, count in sorted(true_counts.items()):
        print(f"  {label}: {count}")

    print(f"\n预测标签 ('{pred_col}') 分布:")
    pred_counts = Counter(y_pred)
    for label, count in sorted(pred_counts.items()):
        print(f"  {label}: {count}")

    # 显示一些不匹配的例子
    if match_count < total_count:
        print(f"\n不匹配样例 (前10个):")
        mismatch_indices = np.where(~matches)[0][:10]
        for idx in mismatch_indices:
            orig_idx = clean_df.index[idx]
            print(f"  行{orig_idx + 2}: 真实='{y_true[idx]}' vs 预测='{y_pred[idx]}'")

    match_stats = {
        'total': total_count,
        'matches': match_count,
        'mismatches': total_count - match_count,
        'match_rate': match_rate
    }

    return y_true, y_pred, len(clean_df), match_stats


def calculate_metrics(y_true, y_pred):
    """
    计算所有分类指标

    Args:
        y_true (array): 真实标签
        y_pred (array): 预测标签

    Returns:
        dict: 包含所有指标的字典
    """
    print("\n=== 计算分类指标 ===")

    # 准确率
    accuracy = accuracy_score(y_true, y_pred)

    # 获取所有唯一标签
    labels = sorted(list(set(y_true) | set(y_pred)))

    # 计算macro, micro, weighted平均的P/R/F1
    precision_macro, recall_macro, f1_macro, _ = precision_recall_fscore_support(
        y_true, y_pred, average='macro', zero_division=0
    )

    precision_micro, recall_micro, f1_micro, _ = precision_recall_fscore_support(
        y_true, y_pred, average='micro', zero_division=0
    )

    precision_weighted, recall_weighted, f1_weighted, _ = precision_recall_fscore_support(
        y_true, y_pred, average='weighted', zero_division=0
    )

    # 每个类别的详细指标
    precision_per_class, recall_per_class, f1_per_class, support_per_class = precision_recall_fscore_support(
        y_true, y_pred, labels=labels, zero_division=0
    )

    # 混淆矩阵
    cm = confusion_matrix(y_true, y_pred, labels=labels)

    metrics = {
        'accuracy': accuracy,
        'macro': {
            'precision': precision_macro,
            'recall': recall_macro,
            'f1': f1_macro
        },
        'micro': {
            'precision': precision_micro,
            'recall': recall_micro,
            'f1': f1_micro
        },
        'weighted': {
            'precision': precision_weighted,
            'recall': recall_weighted,
            'f1': f1_weighted
        },
        'per_class': {
            'labels': labels,
            'precision': precision_per_class,
            'recall': recall_per_class,
            'f1': f1_per_class,
            'support': support_per_class
        },
        'confusion_matrix': cm
    }

    return metrics


def print_metrics_report(metrics, match_stats):
    """
    打印详细的指标报告

    Args:
        metrics (dict): 指标字典
        match_stats (dict): 匹配统计信息
    """
    print("\n" + "=" * 60)
    print("              分类性能指标报告")
    print("          (dia真实值 vs answer预测值)")
    print("=" * 60)

    # 基本匹配统计
    print(f"\n=== 基本统计 ===")
    print(f"总样本数:     {match_stats['total']}")
    print(f"完全匹配数:   {match_stats['matches']}")
    print(f"不匹配数:     {match_stats['mismatches']}")
    print(f"总体匹配率:   {match_stats['match_rate']:.4f} ({match_stats['match_rate'] * 100:.2f}%)")

    # 准确率（与总体匹配率相同）
    print(f"\n=== 分类指标 ===")
    print(f"准确率 (Accuracy): {metrics['accuracy']:.4f}")

    # 宏平均指标
    print(f"\n宏平均 (Macro Average):")
    print(f"  精确率 (Precision): {metrics['macro']['precision']:.4f}")
    print(f"  召回率 (Recall):    {metrics['macro']['recall']:.4f}")
    print(f"  F1分数 (F1-Score):  {metrics['macro']['f1']:.4f}")

    # 微平均指标
    print(f"\n微平均 (Micro Average):")
    print(f"  精确率 (Precision): {metrics['micro']['precision']:.4f}")
    print(f"  召回率 (Recall):    {metrics['micro']['recall']:.4f}")
    print(f"  F1分数 (F1-Score):  {metrics['micro']['f1']:.4f}")

    # 加权平均指标
    print(f"\n加权平均 (Weighted Average):")
    print(f"  精确率 (Precision): {metrics['weighted']['precision']:.4f}")
    print(f"  召回率 (Recall):    {metrics['weighted']['recall']:.4f}")
    print(f"  F1分数 (F1-Score):  {metrics['weighted']['f1']:.4f}")

    # 每个类别的详细指标
    print(f"\n=== 各类别详细指标 ===")
    print(f"{'类别':<20} {'精确率':<10} {'召回率':<10} {'F1分数':<10} {'支持数':<10}")
    print("-" * 70)

    for i, label in enumerate(metrics['per_class']['labels']):
        precision = metrics['per_class']['precision'][i]
        recall = metrics['per_class']['recall'][i]
        f1 = metrics['per_class']['f1'][i]
        support = metrics['per_class']['support'][i]
        print(f"{str(label):<20} {precision:<10.4f} {recall:<10.4f} {f1:<10.4f} {support:<10}")

    print(f"\n注：支持数表示该类别在真实标签中的样本数量")


def plot_confusion_matrix(cm, labels, file_name, save_path=None):
    """
    绘制混淆矩阵热图

    Args:
        cm (array): 混淆矩阵
        labels (list): 标签列表
        file_name (str): 文件名（用于标题）
        save_path (str): 保存路径，可选
    """
    plt.figure(figsize=(max(10, len(labels) * 0.8), max(8, len(labels) * 0.8)))
    print(labels)
    # 使用seaborn绘制热图
    sns.heatmap(cm,
                annot=True,
                fmt='d',
                cmap='Blues',
                xticklabels=labels,
                yticklabels=labels,
                cbar_kws={'label': '样本数量'})

    plt.title(f'混淆矩阵 (Confusion Matrix) - {file_name}\n真实值(dia) vs 预测值(answer)', fontsize=16, pad=20)
    plt.xlabel('预测值 (answer列)', fontsize=12)
    plt.ylabel('真实值 (dia列)', fontsize=12)

    # 旋转x轴标签以防止重叠
    plt.xticks(rotation=45, ha='right')
    plt.yticks(rotation=0)
    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"混淆矩阵图已保存至: {save_path}")

    plt.show()


def print_confusion_matrix_details(cm, labels):
    """
    打印混淆矩阵的详细信息

    Args:
        cm (array): 混淆矩阵
        labels (list): 标签列表
    """
    print(f"\n=== 混淆矩阵详情 ===")
    print(f"矩阵维度: {cm.shape}")
    print(f"说明: 行表示真实值(dia列)，列表示预测值(answer列)")

    # 打印混淆矩阵
    print(f"\n混淆矩阵:")

    # 创建表头
    header = "真实\\预测".ljust(15)
    for label in labels:
        header += f"{str(label)[:10]:<12}"
    print(header)
    print("-" * len(header))

    # 打印每行
    for i, label in enumerate(labels):
        row = f"{str(label)[:12]:<15}"
        for j in range(len(labels)):
            row += f"{cm[i, j]:<12}"
        print(row)

    # 计算总体统计
    total_samples = cm.sum()
    correct_predictions = np.trace(cm)  # 对角线元素之和

    print(f"\n=== 统计摘要 ===")
    print(f"总样本数:       {total_samples}")
    print(f"正确预测数:     {correct_predictions}")
    print(f"错误预测数:     {total_samples - correct_predictions}")
    print(f"准确率:         {correct_predictions / total_samples:.4f}")

    # 显示各类别的预测情况
    print(f"\n=== 各类别预测详情 ===")
    for i, true_label in enumerate(labels):
        true_count = cm[i, :].sum()  # 该类别的真实样本总数
        correct_count = cm[i, i]  # 该类别预测正确的数量

        if true_count > 0:
            accuracy = correct_count / true_count
            print(f"'{true_label}': 真实数量={true_count}, 预测正确={correct_count}, 准确率={accuracy:.4f}")

            # 显示误分类情况
            for j, pred_label in enumerate(labels):
                if i != j and cm[i, j] > 0:
                    print(f"    误分类为'{pred_label}': {cm[i, j]}个")
        else:
            print(f"'{true_label}': 无真实样本")


def save_results_to_excel(file_path, metrics, match_stats, excel_path='分析结果.xlsx'):
    """
    将分析结果保存到Excel文件

    Args:
        file_path (str): 分析的文件完整路径
        metrics (dict): 计算得到的指标
        match_stats (dict): 匹配统计信息
        image_file_path (str): 生成的图片文件路径
        excel_path (str): 结果Excel文件路径
    """
    # 准备要保存的数据
    result_data = {
        '文件名': file_path,  # 使用完整路径作为文件名标识
        '总样本数量': match_stats['total'],
        '完全匹配数量': match_stats['matches'],
        '不匹配数量': match_stats['mismatches'],
        '总体匹配率': round(match_stats['match_rate'], 4),
        '准确率': round(metrics['accuracy'], 4),
        'Macro-F1': round(metrics['macro']['f1'], 4),
        'Macro-Recall': round(metrics['macro']['recall'], 4),
        'Macro-Precision': round(metrics['macro']['precision'], 4),
        'Micro-F1': round(metrics['micro']['f1'], 4),
        'Micro-Recall': round(metrics['micro']['recall'], 4),
        'Micro-Precision': round(metrics['micro']['precision'], 4),
        'Weighted-F1': round(metrics['weighted']['f1'], 4),
        'Weighted-Recall': round(metrics['weighted']['recall'], 4),
        'Weighted-Precision': round(metrics['weighted']['precision'], 4),


        #'图片文件': os.path.basename(image_file_path)
    }

    # 检查Excel文件是否存在
    if os.path.exists(excel_path):
        # 如果存在，读取现有数据
        try:
            existing_df = pd.read_excel(excel_path)
            # 检查是否已经有该文件的记录
            if file_path in existing_df['文件名'].values:
                # 更新现有记录
                mask = existing_df['文件名'] == file_path
                for key, value in result_data.items():
                    if key in existing_df.columns:
                        existing_df.loc[mask, key] = value
                result_df = existing_df
                print(f"更新了文件 '{file_path}' 的分析结果")
            else:
                # 追加新记录
                new_row_df = pd.DataFrame([result_data])
                result_df = pd.concat([existing_df, new_row_df], ignore_index=True)
                print(f"添加了文件 '{file_path}' 的分析结果")
        except Exception as e:
            print(f"读取现有Excel文件时出错: {e}")
            # 创建新的DataFrame
            result_df = pd.DataFrame([result_data])
    else:
        # 创建新的DataFrame
        result_df = pd.DataFrame([result_data])
        print(f"创建新的分析结果文件，添加了文件 '{file_path}' 的结果")

    # 保存到Excel
    try:
        result_df.to_excel(excel_path, index=False)
        print(f"分析结果已保存到: {excel_path}")
    except Exception as e:
        print(f"保存Excel文件时出错: {e}")


def analyze_file(file_path):
    """
    分析单个文件

    Args:
        file_path (str): 要分析的Excel文件路径

    Returns:
        bool: 分析是否成功
    """
    print(f"\n{'=' * 80}")
    print(f"开始分析文件: {file_path}")
    print(f"{'=' * 80}")

    # 读取Excel文件
    df = read_excel_file(file_path)
    if df is None:
        return False

    # 验证列是否存在
    if not validate_columns(df, 'norm_syndrome', 'answer'):
        return False

    # 预处理数据
    y_true, y_pred, valid_samples, match_stats = preprocess_data(df, 'norm_syndrome', 'answer')

    if valid_samples == 0:
        print("错误: 没有有效的数据行可以进行分析")
        return False

    # 计算指标
    metrics = calculate_metrics(y_true, y_pred)

    # 打印详细报告
    print_metrics_report(metrics, match_stats)

    # 打印混淆矩阵详情
    #print_confusion_matrix_details(metrics['confusion_matrix'], metrics['per_class']['labels'])

    # 生成图片文件名（基于文件的完整路径，避免同名文件覆盖）
    # file_path_safe = file_path.replace('\\', '_').replace('/', '_')
    # image_file_path = f'confusion_matrix_{file_path_safe}.png'

    # 绘制混淆矩阵
    # plot_confusion_matrix(metrics['confusion_matrix'],
    #                      metrics['per_class']['labels'],
    #                      os.path.basename(file_path),
    #                      save_path=image_file_path)

    # 保存结果到Excel（传递完整文件路径）
    save_results_to_excel(file_path, metrics, match_stats)

    print(f"\n=== 文件 '{file_path}' 分析完成 ===")
    print(f"共处理 {valid_samples} 个有效样本")
    print(f"总体匹配率: {match_stats['match_rate']:.4f} ({match_stats['match_rate'] * 100:.2f}%)")
    # print(f"混淆矩阵图已保存为: {image_file_path}")

    return True


def find_all_xlsx_files(folder_path):
    """
    递归查找指定文件夹中的所有xlsx文件

    Args:
        folder_path (str): 文件夹路径

    Returns:
        list: 所有找到的xlsx文件路径列表
    """
    xlsx_files = []
    for root, dirs, files in os.walk(folder_path):
        for file in files:
            if file.endswith('.xlsx'):
                xlsx_files.append(os.path.join(root, file))
    return xlsx_files


def main():
    """
    主函数
    """
    print("=" * 80)
    print("数据校对分析程序")
    print("=" * 80)

    folder_path = "LLM_G(1)"
    if not os.path.exists(folder_path):
        print(f"错误: 文件夹 '{folder_path}' 不存在")
        return

    # 递归查找所有xlsx文件
    excel_files = find_all_xlsx_files(folder_path)
    
    if not excel_files:
        print(f"未在文件夹 '{folder_path}' 中找到任何xlsx文件")
        return

    print(f"找到 {len(excel_files)} 个Excel文件需要分析:")
    for i, file_path in enumerate(excel_files, 1):
        print(f"{i}. {file_path}")
    print()

    # 遍历并分析每个文件
    successful_count = 0
    failed_count = 0

    for file_path in excel_files:
        print("\n开始执行分类指标计算...")
        print(f"目标文件: {file_path}")
        print("分析目标: 比较dia列(真实值)与answer列(预测值)的匹配情况")
        print("结果将保存到: 分析结果.xlsx")
        success = analyze_file(file_path)
        
        if success:
            successful_count += 1
        else:
            failed_count += 1
            print(f"分析文件 {file_path} 时出现错误")

    # 汇总结果
    print(f"\n{'=' * 80}")
    print("所有分析任务完成！")
    print(f"成功分析: {successful_count} 个文件")
    print(f"分析失败: {failed_count} 个文件")
    print("- 详细分析结果已输出到控制台")
    print("- 汇总结果已保存到 '分析结果.xlsx'")
    print("- 混淆矩阵图片已按文件名保存")
    print(f"{'=' * 80}")


if __name__ == "__main__":
    main()